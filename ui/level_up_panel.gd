extends Control
class_name LevelUpPanel

## 升级弹窗面板
##
## 负责显示玩家升级时的选择界面，包括：
## - 当前拥有的弹球和遗物展示
## - 升级选项的显示和选择
## - 物品详细信息的展示
## - 升级确认功能

# 信号定义
signal upgrade_selected(upgrade_data: Dictionary)
signal panel_closed()

# UI节点引用
@onready var title_label: Label = $MainPanel/MainContainer/VBoxContainer/TitleSection/TitleLabel
@onready var level_label: Label = $MainPanel/MainContainer/VBoxContainer/TitleSection/LevelLabel
@onready var balls_title: Label = $MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/BallsSection/BallsTitle
@onready var balls_grid: GridContainer = $MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/BallsSection/BallsGridContainer/BallsGrid
@onready var relics_title: Label = $MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/RelicsSection/RelicsTitle
@onready var relics_grid: GridContainer = $MainPanel/MainContainer/VBoxContainer/OwnedItemsSection/RelicsSection/RelicsGridContainer/RelicsGrid
@onready var effect_description: RichTextLabel = $MainPanel/MainContainer/VBoxContainer/EffectDescriptionSection/EffectDescriptionMargin/EffectDescriptionLabel
@onready var upgrade_options_grid: GridContainer = $MainPanel/MainContainer/VBoxContainer/UpgradeOptionsSection/UpgradeOptionsGridContainer/UpgradeOptionsGrid
@onready var confirm_button: Button = $MainPanel/MainContainer/VBoxContainer/ConfirmSection/ConfirmButton

# 数据存储
var current_balls: Array[Dictionary] = []
var current_relics: Array[Dictionary] = []
var upgrade_options: Array[Dictionary] = []
var selected_upgrade_index: int = -1

# 物品槽预制场景
const ITEM_SLOT_SCENE: PackedScene = preload("res://ui/prefab/item_slot.tscn")
const UPGRADE_OPTION_SCENE: PackedScene = preload("res://ui/prefab/upgrade_option.tscn")

# 常量定义
const MAX_BALLS: int = 10
const MAX_RELICS: int = 10
const ITEM_SLOT_SIZE = Vector2(56, 56)  # 优化后的触摸目标尺寸，提升可视性和触摸体验

func _ready() -> void:
	# 连接确认按钮信号
	confirm_button.pressed.connect(_on_confirm_button_pressed)

	# 初始化物品槽
	_initialize_item_slots()

	# 设置初始状态
	_reset_effect_description()

	# 设置暂停模式以确保在游戏暂停时仍能接收输入
	process_mode = Node.PROCESS_MODE_WHEN_PAUSED

	print("升级弹窗面板初始化完成")
	print("  process_mode:", process_mode)


## 显示升级面板
## @param level_data: 包含当前等级信息的字典
## @param balls_data: 当前拥有的弹球数据数组
## @param relics_data: 当前拥有的遗物数据数组
## @param options_data: 升级选项数据数组
func show_panel(level_data: Dictionary, balls_data: Array, relics_data: Array, options_data: Array) -> void:
	print("=== 升级面板显示 ===")
	print("操作类型：显示升级面板")
	print("等级变化：", level_data.get("current_level", 1), " → ", level_data.get("next_level", 2))
	print("弹球数据数量：", balls_data.size())
	print("遗物数据数量：", relics_data.size())
	print("升级选项数量：", options_data.size())

	# 更新等级信息
	_update_level_info(level_data)

	# 更新物品数据（类型转换）
	current_balls.clear()
	for ball in balls_data:
		current_balls.append(ball as Dictionary)

	current_relics.clear()
	for relic in relics_data:
		current_relics.append(relic as Dictionary)

	upgrade_options.clear()
	for option in options_data:
		upgrade_options.append(option as Dictionary)

	# 更新UI显示
	_update_balls_display()
	_update_relics_display()
	_update_upgrade_options()

	# 重置选择状态
	selected_upgrade_index = -1
	_update_confirm_button()
	_reset_effect_description()

	# 暂停游戏
	get_tree().paused = true
	# 显示面板
#	visible = true
	GuiTransitions.show("LevelUpPanel")
	await GuiTransitions.show_completed

	print("游戏已暂停，等待玩家选择升级")
	print("升级面板显示完成")
	print("==================")

## 隐藏升级面板
func hide_panel() -> void:
	print("=== 升级面板隐藏 ===")
	print("操作类型：隐藏升级面板")
	print("当前选择的升级选项：", selected_upgrade_index)

#	visible = false
	GuiTransitions.hide("LevelUpPanel")
	await GuiTransitions.hide_completed
	print("面板可见性设置为false")

	# 恢复游戏
	get_tree().paused = false
	print("游戏暂停状态已恢复")

	# 发送面板关闭信号
	panel_closed.emit()
	print("发送panel_closed信号")
	print("升级面板隐藏完成")
	print("==================")

## 初始化物品槽
func _initialize_item_slots() -> void:
	print("初始化物品槽网格")

	# 创建弹球槽位
	for i in range(MAX_BALLS):
		var ball_slot: ItemSlot = _create_item_slot()
		# 使用闭包正确传递索引和数据
		ball_slot.item_clicked.connect(func(item_data: Dictionary): _on_ball_slot_clicked(i, item_data))
		balls_grid.add_child(ball_slot)

	# 创建遗物槽位
	for i in range(MAX_RELICS):
		var relic_slot: ItemSlot = _create_item_slot()
		# 使用闭包正确传递索引和数据
		relic_slot.item_clicked.connect(func(item_data: Dictionary): _on_relic_slot_clicked(i, item_data))
		relics_grid.add_child(relic_slot)

## 创建物品槽
func _create_item_slot() -> ItemSlot:
	var slot: ItemSlot = ITEM_SLOT_SCENE.instantiate() as ItemSlot
	return slot

## 更新等级信息显示
func _update_level_info(level_data: Dictionary) -> void:
	var current_level = level_data.get("current_level", 1)
	var next_level = level_data.get("next_level", current_level + 1)

	level_label.text = "等级 %d → %d" % [current_level, next_level]
	print("更新等级信息：", level_label.text)

## 更新弹球显示
func _update_balls_display() -> void:
	print("更新弹球显示，当前拥有：", current_balls.size(), "/", MAX_BALLS)

	balls_title.text = "🏀 拥有弹球 (%d/%d)" % [current_balls.size(), MAX_BALLS]

	# 更新每个弹球槽位
	for i in range(MAX_BALLS):
		var slot: ItemSlot = balls_grid.get_child(i) as ItemSlot
		if i < current_balls.size():
			var ball_data: Dictionary = current_balls[i]
			slot.set_item_data(ball_data)
		else:
			slot.set_empty()

## 更新遗物显示
func _update_relics_display() -> void:
	print("更新遗物显示，当前拥有：", current_relics.size(), "/", MAX_RELICS)

	relics_title.text = "💎 拥有遗物 (%d/%d)" % [current_relics.size(), MAX_RELICS]

	# 更新每个遗物槽位
	for i in range(MAX_RELICS):
		var slot: ItemSlot = relics_grid.get_child(i) as ItemSlot
		if i < current_relics.size():
			var relic_data: Dictionary = current_relics[i]
			slot.set_item_data(relic_data)
		else:
			slot.set_empty()

## 更新升级选项显示
func _update_upgrade_options() -> void:
	print("更新升级选项，可选项数量：", upgrade_options.size())

	# 清除现有选项
	for child in upgrade_options_grid.get_children():
		child.queue_free()

	# 创建新的升级选项
	for i in range(upgrade_options.size()):
		var option_data: Dictionary = upgrade_options[i]
		var option_button: Button = _create_upgrade_option(option_data, i)
		upgrade_options_grid.add_child(option_button)

## 创建升级选项按钮
func _create_upgrade_option(option_data: Dictionary, index: int) -> Button:
	var option_button = Button.new()

	# 设置正方形尺寸，与物品槽保持一致的视觉风格
	var button_size = 100  # 正方形尺寸，比物品槽稍大以突出重要性
	option_button.custom_minimum_size = Vector2(button_size, button_size)
	option_button.size_flags_horizontal = 0  # 不扩展，保持固定尺寸
	option_button.size_flags_vertical = 0

	# 设置按钮文本
	var option_icon = option_data.get("icon", "⭐")
	var option_name = option_data.get("name", "未知选项")
	option_button.text = "%s\n%s" % [option_icon, option_name]

	# 连接信号
	option_button.pressed.connect(_on_upgrade_option_selected.bind(option_data, index))

	print("创建升级选项按钮：", option_name, "，尺寸：", Vector2(button_size, button_size))

	return option_button

## 弹球槽位点击处理
func _on_ball_slot_clicked(index: int, item_data: Dictionary) -> void:
	print("=== 弹球槽位交互 ===")
	print("操作类型：点击弹球槽位")
	print("槽位索引：", index)
	print("物品名称：", item_data.get("name", "未知弹球"))
	print("物品图标：", item_data.get("icon", "❓"))
	print("物品描述：", item_data.get("description", "暂无描述"))
	print("物品效果：", item_data.get("effects", []))
	print("当前弹球总数：", current_balls.size(), "/", MAX_BALLS)

	_show_item_description(item_data)
	print("效果说明区域已更新显示弹球信息")
	print("==================")

## 遗物槽位点击处理
func _on_relic_slot_clicked(index: int, item_data: Dictionary) -> void:
	print("=== 遗物槽位交互 ===")
	print("操作类型：点击遗物槽位")
	print("槽位索引：", index)
	print("物品名称：", item_data.get("name", "未知遗物"))
	print("物品图标：", item_data.get("icon", "❓"))
	print("物品描述：", item_data.get("description", "暂无描述"))
	print("物品效果：", item_data.get("effects", []))
	print("当前遗物总数：", current_relics.size(), "/", MAX_RELICS)

	_show_item_description(item_data)
	print("效果说明区域已更新显示遗物信息")
	print("==================")

## 升级选项选择处理
func _on_upgrade_option_selected(option_data: Dictionary, index: int) -> void:
	print("=== 升级选项交互 ===")
	print("操作类型：选择升级选项")
	print("选项索引：", index)
	print("选项名称：", option_data.get("name", "未知选项"))
	print("选项图标：", option_data.get("icon", "⭐"))
	print("选项类型：", option_data.get("type", "未知类型"))
	print("选项描述：", option_data.get("description", "暂无描述"))
	print("选项效果：", option_data.get("effects", []))
	print("之前选择的选项索引：", selected_upgrade_index)

	# 更新选择状态
	selected_upgrade_index = index
	print("新选择的选项索引：", selected_upgrade_index)

	# 更新选项按钮外观
	_update_option_buttons_appearance()
	print("升级选项按钮外观已更新")

	# 显示选项详细信息
	_show_item_description(option_data)
	print("效果说明区域已更新显示选项信息")

	# 更新确认按钮状态
	_update_confirm_button()
	print("确认按钮状态已更新")
	print("===================")

## 更新选项按钮外观
func _update_option_buttons_appearance() -> void:
	print("--- 更新升级选项按钮外观 ---")
	print("当前选择的选项索引：", selected_upgrade_index)
	print("升级选项按钮总数：", upgrade_options_grid.get_child_count())

	for i in range(upgrade_options_grid.get_child_count()):
		var button: Button = upgrade_options_grid.get_child(i) as Button
		if i == selected_upgrade_index:
			button.modulate = Color(1.2, 1.2, 0.8)  # 高亮选中项
			print("按钮", i, "设置为选中状态（高亮）")
		else:
			button.modulate = Color.WHITE
			print("按钮", i, "设置为未选中状态（正常）")

	print("---------------------------")

## 显示物品描述
func _show_item_description(item_data: Dictionary) -> void:
	var name = item_data.get("name", "未知物品")
	var description = item_data.get("description", "暂无描述")
	var effects = item_data.get("effects", [])

	print("--- 更新效果说明区域 ---")
	print("物品名称：", name)
	print("物品描述：", description)
	print("效果数量：", effects.size())

	var description_text = "[center][b]%s[/b][/center]\n\n%s" % [name, description]

	if effects.size() > 0:
		description_text += "\n\n[b]效果：[/b]"
		for effect in effects:
			description_text += "\n• %s" % effect
			print("效果项：", effect)

	effect_description.text = description_text
	print("效果说明区域文本已更新")
	print("------------------------")

## 重置效果描述
func _reset_effect_description() -> void:
	effect_description.text = "[center][color=#999999][i]点击物品或升级选项查看详细说明[/i][/color][/center]"

## 更新确认按钮状态
func _update_confirm_button() -> void:
	print("--- 更新确认按钮状态 ---")
	print("当前选择的升级选项索引：", selected_upgrade_index)

	if selected_upgrade_index >= 0:
		confirm_button.disabled = false
		confirm_button.text = "确认升级"
		print("确认按钮状态：启用，文本：'确认升级'")
	else:
		confirm_button.disabled = true
		confirm_button.text = "请先选择一个升级选项"
		print("确认按钮状态：禁用，文本：'请先选择一个升级选项'")

	print("------------------------")

## 确认按钮点击处理
func _on_confirm_button_pressed() -> void:
	print("=== 确认按钮交互 ===")
	print("操作类型：点击确认按钮")
	print("当前选择的升级选项索引：", selected_upgrade_index)
	print("升级选项总数：", upgrade_options.size())

	if selected_upgrade_index >= 0 and selected_upgrade_index < upgrade_options.size():
		var selected_option: Dictionary = upgrade_options[selected_upgrade_index]
		print("确认升级选择有效")
		print("选择的升级选项：", selected_option.get("name", "未知选项"))
		print("选项类型：", selected_option.get("type", "未知类型"))
		print("选项效果：", selected_option.get("effects", []))

		# 发送升级选择信号
		print("发送upgrade_selected信号")
		upgrade_selected.emit(selected_option)

		# 隐藏面板
		print("隐藏升级面板")
		hide_panel()
		print("升级确认流程完成")
	else:
		print("错误：无效的升级选择")
		print("选择索引超出范围或未选择任何选项")

	print("===================")


## 获取示例数据（用于测试）
func get_sample_data() -> Dictionary:
	return {
		"level_data": {
			"current_level": 5,
			"next_level": 6
		},
		"balls_data": [
			{"name": "基础弹球", "icon": "⚽", "description": "最基础的攻击弹球", "effects": ["伤害: 10", "速度: 100"]},
			{"name": "火焰弹球", "icon": "🔥", "description": "造成火焰伤害的弹球", "effects": ["伤害: 15", "附加燃烧效果"]},
			{"name": "冰霜弹球", "icon": "❄️", "description": "造成冰霜伤害的弹球", "effects": ["伤害: 12", "附加减速效果"]}
		],
		"relics_data": [
			{"name": "力量护符", "icon": "💪", "description": "增强攻击力的护符", "effects": ["攻击力 +20%"]},
			{"name": "速度之靴", "icon": "👟", "description": "提升移动速度的靴子", "effects": ["移动速度 +15%"]}
		],
		"options_data": [
			{"name": "雷电弹球", "icon": "⚡", "description": "获得新的雷电弹球", "effects": ["伤害: 18", "链式闪电攻击"]},
			{"name": "火焰强化", "icon": "🔥+", "description": "升级现有火焰弹球", "effects": ["伤害: 15 → 25", "燃烧范围扩大"]},
			{"name": "幸运硬币", "icon": "🪙", "description": "获得幸运硬币遗物", "effects": ["金币获得 +50%", "暴击率 +10%"]}
		]
	}
